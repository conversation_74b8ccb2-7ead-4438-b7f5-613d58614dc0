<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试数据生成器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/style.css" />
</head>
<body class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 min-h-screen text-white">
    <div class="container mx-auto p-6">
        <div class="max-w-2xl mx-auto">
            <h1 class="text-3xl font-bold mb-6 text-center">测试数据生成器</h1>
            
            <div class="glass-card p-6 rounded-2xl mb-6">
                <h2 class="text-xl font-semibold mb-4">生成测试数据</h2>
                <p class="text-gray-300 mb-4">点击下面的按钮生成停车场和车辆测试数据</p>
                
                <div class="space-y-4">
                    <button id="generateParkingLots" class="btn-primary w-full">
                        生成停车场数据
                    </button>
                    <button id="generateVehicles" class="btn-primary w-full">
                        生成车辆数据
                    </button>
                    <button id="clearAllData" class="btn-secondary w-full">
                        清空所有数据
                    </button>
                </div>
            </div>
            
            <div class="glass-card p-6 rounded-2xl">
                <h2 class="text-xl font-semibold mb-4">快速测试</h2>
                <div class="space-y-2">
                    <a href="vehicle-entry.html" class="btn-secondary block text-center">车辆入场</a>
                    <a href="vehicle-entry.html?action=exit" class="btn-primary block text-center">车辆出场</a>
                    <a href="index.html" class="btn-secondary block text-center">返回首页</a>
                </div>
            </div>
        </div>
    </div>

    <script src="js/storage.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 生成停车场数据
            document.getElementById('generateParkingLots').addEventListener('click', function() {
                const parkingLots = [
                    {
                        name: '中央商务区停车场',
                        location: '市中心CBD',
                        totalSpaces: 200,
                        hourlyRate: 8
                    },
                    {
                        name: '购物中心停车场',
                        location: '万达广场',
                        totalSpaces: 150,
                        hourlyRate: 6
                    },
                    {
                        name: '医院停车场',
                        location: '人民医院',
                        totalSpaces: 100,
                        hourlyRate: 5
                    }
                ];

                parkingLots.forEach(lot => {
                    storage.addParkingLot(lot);
                });

                alert('停车场数据生成完成！');
            });

            // 生成车辆数据
            document.getElementById('generateVehicles').addEventListener('click', function() {
                const parkingLots = storage.getParkingLots();
                if (parkingLots.length === 0) {
                    alert('请先生成停车场数据！');
                    return;
                }

                const provinces = ['京', '津', '沪', '渝', '冀', '豫', '云', '辽', '黑', '湘', '皖', '鲁', '新', '苏', '浙', '赣', '鄂', '桂', '甘', '晋', '蒙', '陕', '吉', '闽', '贵', '粤', '青', '藏', '川', '宁', '琼'];
                const letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
                const numbers = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';

                // 生成一些测试车牌号
                const testPlates = [
                    '京A12345',
                    '京B67890',
                    '沪C11111',
                    '粤D22222',
                    '川E33333',
                    '浙F44444',
                    '苏G55555',
                    '鲁H66666',
                    '豫J77777',
                    '鄂K88888'
                ];

                testPlates.forEach((plate, index) => {
                    const randomParkingLot = parkingLots[Math.floor(Math.random() * parkingLots.length)];
                    const entryTime = new Date();
                    entryTime.setHours(entryTime.getHours() - Math.floor(Math.random() * 12)); // 随机入场时间（0-12小时前）

                    const vehicle = {
                        licensePlate: plate,
                        parkingLotId: randomParkingLot.id,
                        entryTime: entryTime.toISOString(),
                        exitTime: null,
                        status: 'parked'
                    };

                    // 直接添加到storage
                    const vehicles = storage.getVehicles();
                    vehicle.id = Date.now().toString() + index;
                    vehicles.push(vehicle);
                    storage.saveVehicles(vehicles);
                });

                alert('车辆数据生成完成！');
            });

            // 清空所有数据
            document.getElementById('clearAllData').addEventListener('click', function() {
                if (confirm('确定要清空所有数据吗？此操作不可恢复！')) {
                    localStorage.clear();
                    storage.initializeStorage();
                    alert('所有数据已清空！');
                }
            });
        });
    </script>
</body>
</html>
